<template>
  <!--  <div class="e4s-flex-row e4s-justify-flex-row-vert-center e4s-flex-grow">-->
  <!--    <InputDebounce-->
  <!--      class="e4s-square&#45;&#45;right e4s-flex-grow"-->
  <!--      :class="filterSearchTerm.length ? 'e4s-input-field&#45;&#45;highlight' : ''"-->
  <!--      placeholder="Search..."-->
  <!--      :value="filterSearchTerm"-->
  <!--      @input="onSearchTermChanged"-->
  <!--    />-->
  <!--    <ButtonGenericV2-->
  <!--      class="e4s-button&#45;&#45;auto"-->
  <!--      with-input="right"-->
  <!--      @click="onSearchTermClear"-->
  <!--      :disabled="filterSearchTerm.length === 0"-->
  <!--      slot="after"-->
  <!--      text="X"-->
  <!--    />-->
  <!--  </div>-->
  <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
    <div class="e4s-flex-row e4s-justify-flex-row-vert-center e4s-flex-grow">
      <InputDebounce
        class="e4s-square--right e4s-flex-grow"
        :class="filterSearchTerm.length ? 'e4s-input-field--highlight' : ''"
        placeholder="Search..."
        :value="filterSearchTerm"
        @input="onSearchTermChanged"
      />
      <ButtonGenericV2
        class="e4s-button--auto"
        with-input="right"
        @click="onSearchTermClear"
        :disabled="filterSearchTerm.length === 0"
        slot="after"
        text="X"
      />
    </div>
    <div class="e4s-flex-row--end">
      <slot name="after-search" />
    </div>
  </div>

  <!--SEEDED_ENTRIES-->
  <div
    v-for="(individualEntries, heatNo) in state.getEntriesGroupedByHeatNo"
    :key="heatNo"
    class="e4s-flex-column-x"
  >
    <div class="e4s-vertical-spacer--standard"></div>
    <div class="e4s-flex-column e4s-lite--header-race-heat">
      <div class="e4s-flex-row">
        <div v-if="state.isTrack ? true : state.howManyHeats > 1" class="e4s-header--400">
          <span v-text="state.isTrack ? 'Race' : 'Heat'"></span>
          {{ heatNo }} of {{ state.howManyHeats }}
          {{ props.compEvent.egOptions.seed.seeded ? '(Seeded)' : '' }}
        </div>
      </div>

      <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
        <div
          class="e4s-flex-column e4s-justify-flex-end e4s-header--500 hybrid-start-race--first-col"
        >
          <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
            Lane
            <PulseIndicator
              color="red"
              :animate="false"
              class="e4s-flex-row--end"
              style="margin-right: var(--e4s-gap--standard)"
            />
          </div>
        </div>
        <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
          = Athlete DNS
        </div>
        <div class="e4s-flex-column e4s-justify-flex-end e4s-flex-row--end e4s-header--500">
          Estimated
        </div>
      </div>
    </div>

    <div class="e4s-vertical-spacer--standard"></div>
    <hr class="dat-e4s-hr-only dat-e4s-hr--light" />
    <HybridStartRace
      v-for="individualEntry in individualEntries"
      :key="individualEntry.entryId"
      :is-track="state.isTrack"
      :pof10="individualEntry.pof10"
      :urn="individualEntry.URN"
    >
      <template #first-col>
        <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
          <span class="e4s-lite-number e4s-lite-number--position-lane">{{
            getLaneNo(individualEntry)
          }}</span>
          <PulseIndicator
            v-if="!individualEntry.present"
            color="red"
            :animate="false"
            class="e4s-flex-row--end"
            style="margin-right: var(--e4s-gap--standard)"
          />
        </div>
      </template>
      <template #bibNo>
        <span class="e4s-lite-number">{{ getBibNo(individualEntry) }}</span>
      </template>
      <template #heatNo>
        {{ getHeatNo(individualEntry) }}
      </template>
      <template #laneNo>
        {{ getLaneNo(individualEntry) }}
      </template>
      <template #athlete-name>
        <div
          class="e4s-flex-row e4s-gap--small e4s-align-items-center e4s-justify-flex-row-vert-center e4s-lite-table--standard-text"
        >
          {{ individualEntry.firstName + ' ' + individualEntry.surName }}
        </div>
      </template>
      <template #affiliation>
        {{ individualEntry.clubName }}
      </template>
      <template #ageGroup>
        {{ '(' + individualEntry.ageGroup + ')' }}
      </template>
      <template #performance>
        <span class="e4s-lite-number e4s-lite-number--score">{{
          getScoreToDisplay(individualEntry.perf, state.isTrack)
        }}</span>
      </template>

      <template #sb-perf>
        {{ getPo10Value(individualEntry.pof10, 'SB', state.isTrack) }}
      </template>
      <template #pb-perf>
        {{ getPo10Value(individualEntry.pof10, 'PB', state.isTrack) }}
      </template>
      <template #po10-link>
        <PowerOfTenLink :urn="individualEntry.URN" image-height="16px" />
      </template>
    </HybridStartRace>
  </div>
  <!--/SEEDED_ENTRIES-->

  <!--TBS_ENTRIES-->
  <div class="e4s-flex-column e4s-lite--header-race-heat" v-if="state.toBeSeeded.length > 0">
    <div class="e4s-flex-row">
      <div v-if="state.isTrack ? true : state.howManyHeats > 1" class="e4s-header--400">
        <span v-text="state.isTrack ? 'Race' : 'Heat'"></span>
        TBS
      </div>
    </div>

    <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
      <div
        class="e4s-flex-column e4s-justify-flex-end e4s-header--500 hybrid-start-race--first-col"
      >
        <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
          Lane
          <PulseIndicator
            color="red"
            :animate="false"
            class="e4s-flex-row--end"
            style="margin-right: var(--e4s-gap--standard)"
          />
        </div>
      </div>
      <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
        = Athlete DNS
      </div>
      <div class="e4s-flex-column e4s-justify-flex-end e4s-flex-row--end e4s-header--500">
        Estimated
      </div>
    </div>
  </div>
  <HybridStartRace
    v-for="individualEntry in state.toBeSeeded"
    :key="individualEntry.entryId"
    :is-track="state.isTrack"
    :pof10="individualEntry.pof10"
    :urn="individualEntry.URN"
  >
    <template #first-col>
      <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
        <span class="e4s-lite-number e4s-lite-number--position-lane">{{
          getLaneNo(individualEntry)
        }}</span>
        <PulseIndicator
          v-if="!individualEntry.present"
          color="red"
          :animate="false"
          class="e4s-flex-row--end"
          style="margin-right: var(--e4s-gap--standard)"
        />
      </div>
    </template>
    <template #bibNo>
      <span class="e4s-lite-number">{{ getBibNo(individualEntry) }}</span>
    </template>
    <template #heatNo>
      {{ getHeatNo(individualEntry) }}
    </template>
    <template #laneNo>
      {{ getLaneNo(individualEntry) }}
    </template>
    <template #athlete-name>
      <div
        class="e4s-flex-row e4s-gap--small e4s-align-items-center e4s-justify-flex-row-vert-center e4s-lite-table--standard-text"
      >
        {{ individualEntry.firstName + ' ' + individualEntry.surName }}
      </div>
    </template>
    <template #affiliation>
      {{ individualEntry.clubName }}
    </template>
    <template #ageGroup>
      {{ '(' + individualEntry.ageGroup + ')' }}
    </template>
    <template #performance>
      <span class="e4s-lite-number e4s-lite-number--score">{{
        getScoreToDisplay(individualEntry.perf, state.isTrack)
      }}</span>
    </template>

    <template #sb-perf>
      {{ getPo10Value(individualEntry.pof10, 'SB', state.isTrack) }}
    </template>
    <template #pb-perf>
      {{ getPo10Value(individualEntry.pof10, 'PB', state.isTrack) }}
    </template>
    <template #po10-link>
      <PowerOfTenLink :urn="individualEntry.URN" image-height="16px" />
    </template>
  </HybridStartRace>
  <!--/TBS_ENTRIES-->

  <!--  TBS_NOT_ELIGIBLE-->
  <!--  state.notEligible.length > 0-->
  <div v-if="false">
    <div class="e4s-flex-column e4s-lite--header-race-heat">
      <div class="e4s-flex-row">
        <div v-if="state.isTrack ? true : state.howManyHeats > 1" class="e4s-header--400">
          <span v-text="state.isTrack ? 'Race' : 'Heat'"></span>
          Not Eligible!
        </div>
      </div>

      <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
        <div
          class="e4s-flex-column e4s-justify-flex-end e4s-header--500 hybrid-start-race--first-col"
        >
          <div class="e4s-flex-row e4s-justify-flex-row-vert-center">
            Lane
            <PulseIndicator
              color="red"
              :animate="false"
              class="e4s-flex-row--end"
              style="margin-right: var(--e4s-gap--standard)"
            />
          </div>
        </div>
        <div class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center">
          = Athlete DNS
        </div>
        <div class="e4s-flex-column e4s-justify-flex-end e4s-flex-row--end e4s-header--500">
          Estimated
        </div>
      </div>
    </div>
    <HybridStartRace
      v-for="individualEntry in state.notEligible"
      :key="individualEntry.entryId"
      :is-track="state.isTrack"
      :pof10="individualEntry.pof10"
      :urn="individualEntry.URN"
    >
      <template #first-col>
        <div class="e4s-flex-row e4s-gap--small e4s-justify-flex-row-vert-center">
          <span class="e4s-lite-number e4s-lite-number--position-lane">{{
            getLaneNo(individualEntry)
          }}</span>
          <PulseIndicator
            v-if="!individualEntry.present"
            color="red"
            :animate="false"
            class="e4s-flex-row--end"
            style="margin-right: var(--e4s-gap--standard)"
          />
        </div>
      </template>
      <template #bibNo>
        <span class="e4s-lite-number">{{ getBibNo(individualEntry) }}</span>
      </template>
      <template #heatNo>
        {{ getHeatNo(individualEntry) }}
      </template>
      <template #laneNo>
        {{ getLaneNo(individualEntry) }}
      </template>
      <template #athlete-name>
        <div
          class="e4s-flex-row e4s-gap--small e4s-align-items-center e4s-justify-flex-row-vert-center e4s-lite-table--standard-text"
        >
          {{ individualEntry.firstName + ' ' + individualEntry.surName }}
        </div>
      </template>
      <template #affiliation>
        {{ individualEntry.clubName }}
      </template>
      <template #ageGroup>
        {{ '(' + individualEntry.ageGroup + ')' }}
      </template>
      <template #performance>
        <span class="e4s-lite-number e4s-lite-number--score">{{
          getScoreToDisplay(individualEntry.perf, state.isTrack)
        }}</span>
      </template>

      <template #sb-perf>
        {{ getPo10Value(individualEntry.pof10, 'SB', state.isTrack) }}
      </template>
      <template #pb-perf>
        {{ getPo10Value(individualEntry.pof10, 'PB', state.isTrack) }}
      </template>
      <template #po10-link>
        <PowerOfTenLink :urn="individualEntry.URN" image-height="16px" />
      </template>
    </HybridStartRace>
  </div>

  <!--  /TBS_NOT_ELIGIBLE-->
</template>

<script setup lang="ts">
import { type PropType, reactive, watch } from 'vue'
import type {
  CompEvent,
  HeatNoString,
  IndividualEntry,
  StoreCompResultsState
} from '@/components/results/store-comp-results'
import * as ResultsService from '@/components/results/results-service'
import { getBibNo, getPo10Value, getScoreToDisplay } from '@/components/results/results-service'
import PowerOfTenLink from '@/common/ui/PowerOfTenLink.vue'
import HybridStartRace from '@/components/results/HybridStartRace.vue'
import InputDebounce from '@/common/ui/fields/input-debounce.vue'
import { simpleClone } from '@/services/common-service-utils'
import ButtonGenericV2 from '@/common/ui/buttons/button-generic-v2.vue'
import PulseIndicator from '@/common/ui/icons/PulseIndicator.vue'

const props = defineProps({
  storeCompResultsState: {
    type: Object as PropType<StoreCompResultsState>,
    required: true
  },
  compEvent: {
    type: Object as PropType<CompEvent>,
    required: true
  },
  filterSearchTerm: {
    type: String,
    default: ''
  }
})

const emit = defineEmits<{
  (e: 'searchTermChanged', value: string): void
}>()

const state = reactive({
  filterTerm: '',
  howManyHeats: 0,
  getEntriesGroupedByHeatNo: {} as Record<HeatNoString, IndividualEntry[]>,
  toBeSeeded: [] as IndividualEntry[],
  notEligible: [] as IndividualEntry[],
  isTrack: false
})

watch(
  () => props.compEvent,
  (newVal, oldValue) => {
    console.log('StartList.scheduledEvent changed', newVal, oldValue)
    init()
  }
)

watch(
  () => props.filterSearchTerm,
  (newVal, oldValue) => {
    if (newVal !== state.filterTerm) {
      console.log('StartList.filterSearchTerm changed', newVal, oldValue)
      state.filterTerm = newVal
    }
  }
)

init()

function doFilter(searchTerm: string) {
  state.filterTerm = searchTerm
  init()
}

function init() {
  let compEvent = simpleClone(props.compEvent)

  if (compEvent.entries) {
    //  get

    if (
      props.storeCompResultsState.competitionOnTheDay.options &&
      props.storeCompResultsState.competitionOnTheDay.options.checkIn.enabled
    ) {
      const result = ResultsService.getEntriesGroupedByHeatNoNew(
        props.storeCompResultsState.competitionOnTheDay.options,
        compEvent
      )

      state.howManyHeats = Object.keys(result.entriesGroupedByHeatNo).length
      state.getEntriesGroupedByHeatNo = result.entriesGroupedByHeatNo

      state.toBeSeeded = result.toBeSeeded
      state.notEligible = result.notEligible
    } else {
      const result = ResultsService.getEntriesGroupedByHeatNo(compEvent)
      state.howManyHeats = Object.keys(result).length
      state.getEntriesGroupedByHeatNo = result
    }

    state.isTrack = ResultsService.isTrackEvent(props.compEvent)
  }
}

function getHeatNo(individualEntry: IndividualEntry) {
  if (props.storeCompResultsState.competitionOnTheDay.options.checkIn.enabled) {
    return individualEntry.heatNoCheckedIn
  }
  return individualEntry.seeding.heatNo
}

function getLaneNo(individualEntry: IndividualEntry) {
  if (props.storeCompResultsState.competitionOnTheDay.options.checkIn.enabled) {
    return individualEntry.laneNoCheckedIn
  }
  return individualEntry.seeding.laneNo === 0 ? 'TBS' : individualEntry.seeding.laneNo
}

function onSearchTermClear() {
  emit('searchTermChanged', '')
}

function onSearchTermChanged(searchTerm: string) {
  console.log('StartList.onSearchTermChanged', searchTerm)
  emit('searchTermChanged', searchTerm)
}
</script>
